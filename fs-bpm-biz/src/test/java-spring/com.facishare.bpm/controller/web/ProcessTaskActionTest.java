package com.facishare.bpm.controller.web;


import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.mobile.MTaskAction;
import com.facishare.bpm.controller.mobile.model.MGetTaskInfo;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.LaneBriefTaskVO;
import com.facishare.paas.I18N;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created by cuiyongxu on 16/12/27.
 */
@Slf4j
public class ProcessTaskActionTest extends BaseTest {

    @Autowired
    private ProcessTaskAction processTaskAction;
    @Autowired
    private MTaskAction taskAction;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) processTaskAction).setContextManager(contextManager);
        //((BPMBaseAction) taskAction).setContextManager(contextManager);
    }


    /**
     * 获取个人待办任务列表(目前只获取发起人自己发出的实例任务列表-2017年01月10日)
     * @throws Exception
     */
    @Test
    public void getHandleTaskList() throws Exception {
        GetHandleTaskList.Arg arg = new GetHandleTaskList.Arg();
//      arg.setIsCompleted(false);
        arg.setIsCompleted(false);
        arg.setPageSize(100);
        arg.setPageNumber(1);
        GetHandleTaskList.Result result = processTaskAction.getHandleTaskList(arg);
        for (TaskOutline task : result.getDataList()) {
            log.info("jsonTask = {}", JsonUtil.toPrettyJson(task));
        }
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getDataList().size() > 0);
    }


    /**
     * 执行任务
     * 1.创建对象 2017年02月28日已不在使用
     * 2.更新对象  通过objectId获取对象,并填充var
     * 3.审批任务 执行任务,并填充var
     * 4.opr执行后,填充var
     *
     * @throws Exception
     */
    @Test
    public void completeTask() throws Exception {
        Map map = Maps.newHashMap();
        map.put("name", "孙猴子");
        map.put(BPMConstants.ApproveResult.RESULT, "reject");

        String taskId = "6585072501950b480bc4f011";
        String objectId = "658423aa4954e80001d1a0dd";
        String opinion = "同意";
        CompleteTask.Arg arg = new CompleteTask.Arg();
        arg.setTaskId(taskId);
        arg.setOpinion(opinion);
        arg.setObjectId(objectId);
        arg.setData(map); //创建对象时使用
        arg.setIgnoreNoBlockValidate(Boolean.TRUE);

        CompleteTask.Result result = processTaskAction.completeTask(arg);
        log.info("返回值 = {}", result);
    }

    @Test
    public void operateTask() throws Exception {
        OperateTask.Arg arg = new OperateTask.Arg();
        arg.setTaskId("650c0d173adce14549898cb1");
        arg.setType("resume");
        OperateTask.Result result = processTaskAction.operateTask(arg);
    }


    @Test
    public void completeTask2() throws Exception {
        Map map = Maps.newHashMap();
        map.put(BPMConstants.ApproveResult.RESULT, "agrese");

        String taskId = "6708911036c5e4405fc81071";
        String objectId = "66f384bffd2c720007e4dab9";
        String opinion = "勉强同意";
        CompleteTask.Arg arg = new CompleteTask.Arg();
        arg.setTaskId(taskId);
        arg.setData(map);
        arg.setOpinion(opinion);
        arg.setObjectId(objectId);
        //arg.setData(map); 创建对象时使用

        CompleteTask.Result result = processTaskAction.completeTask(arg);
    }
    /**
     * 按阶段获取流程定义下所有的任务
     */
    @Test
    public void getHandleTaskListByLane() {
        GetHandleTaskListByLane.Arg arg = new GetHandleTaskListByLane.Arg();
        arg.setSourceWorkflowId("6334f7207f65690001ef756c");
//        arg.setLaneId("1488347206717");
//        arg.setActivityId("1488347206716");
        PageResult<LaneBriefTaskVO> result = processTaskAction.getHandleTaskListByLane(arg);

        Assert.assertTrue(result.getTotal() > 0);
    }

    /**
     * 获取任务详情
     */
    @Test
    public void testGetTask() {
        String taskId = "67b5fb800d33cf541272c7a8";
        GetTaskInfo.Arg arg = new GetTaskInfo.Arg();
        arg.setId(taskId);
        GetTaskInfo.Result result = processTaskAction.getTaskInfo(arg);
        log.info("GetTask:{}", JacksonUtil.toJson(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void updateAndComplete() {
        String json = "{\n" +
                "    \"taskId\": \"65b5fba458ceb9659d2ebc6d\",\n" +
                "    \"data\": {\n" +
                "        \"object_describe_api_name\": \"object_8F03i__c\",\n" +
                "        \"object_describe_id\": \"\",\n" +
                "        \"field_31NP6__c\": \"1.00\",\n" +
                "        \"version\": 8,\n" +
                "        \"_id\": \"65a14c07ef22db0001feb36c\"\n" +
                "    },\n" +
                "    \"entityId\": \"object_8F03i__c\",\n" +
                "    \"objectId\": \"65a14c07ef22db0001feb36c\",\n" +
                "    \"ignoreNonBlocking\": true\n" +
                "}";
        UpdateDataAndCompleteTask.Arg arg = JsonUtil.fromJson(json, UpdateDataAndCompleteTask.Arg.class);
        UpdateDataAndCompleteTask.Result result = processTaskAction.updateDataAndCompleteTask(arg);
        log.info("GetTask:{}", JacksonUtil.toJson(result));
        Assert.assertNotNull(result);
    }
    /**
     * 获取当前人的某对象下待办任务列表
     * @throws Exception
     */
    @Test
    public void getUncompletedTasksByObject() throws Exception {

        System.setProperty("-Dspring.profiles.active","fstest");
        GetUncompletedTasksByObject.Arg arg = new GetUncompletedTasksByObject.Arg();
        arg.setObjectId("632bcd6219eadc000125d616");
        arg.setApiName("object_7prLH__c");
        GetUncompletedTasksByObject.Result result = processTaskAction.getUncompletedTasksByObject(arg);
        Assert.assertNotNull(result);
    }

    @Test
    public void changeTaskHandler() throws Exception {
        ChangeTaskHandler.Arg arg = new ChangeTaskHandler.Arg();
        arg.setTaskId("58fd649e3db71d2804505ef8");
        arg.setCandidateIds(Lists.newArrayList("2003"));
        ChangeTaskHandler.Result result = processTaskAction.changeTaskHandler(arg);

        Assert.assertTrue(result.isResult());
    }

    @Test
    public void getWorkflowUncompletedTasks(){
        GetWorkflowTasks.Arg arg =  new GetWorkflowTasks.Arg();
        arg.setSourceWorkflowId("381608295523811328");
        arg.setType(LaneBriefTaskVO.QueryType.activity);
        arg.setId("1533798396233");
        processTaskAction.getWorkflowUncompletedTasks(arg);
    }


    @Test
    public void getTaskInfo(){
        GetTaskInfo.Arg arg = new GetTaskInfo.Arg();
        arg.setId("6526870f017b820629a4a68d");
        arg.setApplyButtons(true);
        arg.setIncludeTaskFeedDetailConfig(true);
        GetTaskInfo.Result result = processTaskAction.getTaskInfo(arg);
        log.info("getTaskInfo:{}", JacksonUtil.toJson(result));
    }

    @Test
    public void getMTaskInfo(){
        MGetTaskInfo.Arg arg = new MGetTaskInfo.Arg();
        arg.setInstanceId("5ee079a03b4c010001c42585");
        arg.setActivityInstanceId("2");
        MGetTaskInfo.Result result = taskAction.getTaskInfo(arg);
        log.info("getTaskInfo:{}", JacksonUtil.toJson(result));
    }

    @Test
    public void getTaskNew(){
        GetTask.Arg arg = new GetTask.Arg();
        arg.setTaskId("63478d90ce9f383dac19eae2");
        GetTask.Result result = processTaskAction.getTask(arg);
        log.info("getTask:{}", JacksonUtil.toJson(result));
        Assert.assertNotNull(result);
    }


    @Test
    public void getUncompletedTasks() throws Exception {
        GetUncompletedTasksByObject.Arg arg = new GetUncompletedTasksByObject.Arg();
        arg.setObjectId("5ee07993dc7c71000171c420");
        arg.setApiName("object_UPfNh__c");
        GetUncompletedTasksByObject.Result result = processTaskAction.getUncompletedTasksByObject(arg);
        log.info("GetUncompletedTasksByObject:{}", JacksonUtil.toJson(result));
        Assert.assertNotNull(result);
    }

    /**
     * 获取任务详情
     */
    @Test
    public void getTasksByLaneId() {

        GetTaskInfoByLaneId.Arg arg = new GetTaskInfoByLaneId.Arg();
//        arg.setInstanceId("63a42fab1562dd2d2e0e72d9");
//        arg.setWorkflowId("63a2f8805b8f6d34c1080f68");
//        arg.setLaneId("1671622024786");
//        arg.setEntityId("object_A3znn__c");
//        arg.setObjectId("63a2daaf4663ce00015fad48");
//        arg.setApplyButtons(true);
//        arg.setNotGetDatas(true);

        arg.setInstanceId("65ea782840e283783d17ebef");
        arg.setWorkflowId("65ea781740e283783d17ebee");
        arg.setLaneId("1709864932258");
        arg.setEntityId("object_7prLH__c");
        arg.setObjectId("65dd8a2b49346b0001c1247e");
        arg.setApplyButtons(true);
        arg.setNotGetDatas(true);

        GetTaskInfoByLaneId.Result taskInfoByLaneId = processTaskAction.getTaskInfoByLaneId(arg);
        log.info("GetTasksByLaneId:{}", JacksonUtil.toJson(taskInfoByLaneId));
        Assert.assertNotNull(taskInfoByLaneId);
    }


    @Test
    public void getTasksByInstanceIds() {

        GetTasksByInstanceIds.Arg arg = new GetTasksByInstanceIds.Arg();
        arg.setWorkflowInstanceId("647e9e02ec96487e35dda854");
        arg.setActivityInstanceIds(Lists.newArrayList("2"));
        GetTasksByInstanceIds.Result tasksByInstanceIds = processTaskAction.getTasksByInstanceIds(arg);
        log.info("tasksByInstanceIds:{}", JacksonUtil.toJson(tasksByInstanceIds));
        Assert.assertNotNull(tasksByInstanceIds);

    }


    @Test
    public void getTaskInfoByLaneId() {
        GetTaskInfoByLaneId.Arg arg = new GetTaskInfoByLaneId.Arg();
        arg.setLaneId("1664347543745");
        arg.setWorkflowId("6334f72026108659329171ee");
        arg.setInstanceId("63478d90ce9f383dac19eae1");
        arg.setEntityId("object_7prLH__c");
        arg.setObjectId("632bcd6219eadc000125d616");
        GetTaskInfoByLaneId.Result tasksByLaneId = processTaskAction.getTaskInfoByLaneId(arg);
        log.info("GetTaskInfoByLaneId:{}", JacksonUtil.toJson(tasksByLaneId));
        Assert.assertNotNull(tasksByLaneId);
    }

    @Test
    public void getTaskInfo2(){
        GetTaskInfo.Arg arg = new GetTaskInfo.Arg();
        arg.setId("63462739ce9f383dac19e28b");
        arg.setApplyButtons(true);
        GetTaskInfo.Result result = processTaskAction.getTaskInfo(arg);
        log.info("getTaskInfo:{}", JacksonUtil.toJson(result));
    }

    @Test
    public void getTask2(){
        GetTask.Arg arg = new GetTask.Arg();
        arg.setTaskId("5ee07a323b4c010001c42588");
        GetTask.Result result = processTaskAction.getTask(arg);
        log.info("getTaskInfo:{}", JacksonUtil.toJson(result));
    }


    @Test
    public void createTaskData() {
        CreateTaskData.Arg arg = new CreateTaskData.Arg();
        CreateTaskData.Result tasksByLaneId = processTaskAction.createTaskData(arg);
        log.info("createTaskData:{}", JacksonUtil.toJson(tasksByLaneId));
        Assert.assertNotNull(tasksByLaneId);
    }

    @Test
    public void getButtonByTaskIds() {

        I18N.setContext("71557", "en");
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);


        GetButtonByTaskIds.Arg arg = new GetButtonByTaskIds.Arg();
        arg.setTaskIds(Sets.newHashSet("63478d90ce9f383dac19eae2"));
        GetButtonByTaskIds.Result buttonByTaskIds = processTaskAction.getButtonByTaskIds(arg);
        log.info("buttonByTaskIds:{}", JacksonUtil.toJson(buttonByTaskIds));
        Assert.assertNotNull(buttonByTaskIds);
    }

    @Test
    public void refreshHandlerByTaskId(){
        RefreshHandlerByTaskId.Arg arg = new RefreshHandlerByTaskId.Arg();
        arg.setTaskId("6275f1800657b943a2e74846");
        processTaskAction.refreshHandlerByTaskId(arg);
        System.out.println();
    }

    @Test
    public void getTaskLogs() throws Exception {
        GetTaskLogs.Arg arg = new GetTaskLogs.Arg();
        GetTaskLogs.Result taskLogs = processTaskAction.getTaskLogs(arg);
        System.out.println(taskLogs);
    }

    @Test
    public void getAllWorkflowTasks(){
        GetWorkflowTasks.Arg arg = new GetWorkflowTasks.Arg();
        arg.setSourceWorkflowId("6334f7207f65690001ef756c");
        arg.setState(LaneBriefTaskVO.QueryState.normal);
        arg.setType(LaneBriefTaskVO.QueryType.activity);
        GetWorkflowTasks.Result allWorkflowTasks = processTaskAction.getAllWorkflowTasks(arg);
        log.info("getAllWorkflowTasks:{}", JacksonUtil.toJson(allWorkflowTasks));
    }
}
